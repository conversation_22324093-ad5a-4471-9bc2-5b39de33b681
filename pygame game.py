import random

import pygame as pg
import json as j
import random as r

# from networkx.classes import non_edges

pg.init()
screen_size = 1024
fps = pg.time.Clock()
screen = pg.display.set_mode((screen_size, screen_size))
pg.display.set_caption("Platformer")
game_running = True

block_size = 64


class Player(pg.sprite.Sprite):
    def __init__(self, name):
        super().__init__()
        """Zagruzka kartinok"""
        self.player_right = []
        self.player_left = []
        """pravo """
        self.player_design = pg.image.load("char_blue/right/char_b_r.png")
        self.player_design_right = pg.transform.scale(self.player_design, (32, 60))
        self.rect = self.player_design.get_rect()
        self.player_design = pg.image.load("char_blue/left/char_b_l.png")
        self.player_design_left = pg.transform.scale(self.player_design, (32, 60))
        """pravo animaticiya """
        for i in range(2):
            time = pg.image.load(f"char_blue/right/char_b_walk_r_{i}.png")
            time = pg.transform.scale(time, (32, 60))
            self.player_right.append(time)
        """levo """
        for i in range(2):
            time = pg.image.load(f"char_blue/left/char_b_walk_l_{i}.png")
            time = pg.transform.scale(time, (32, 60))
            self.player_left.append(time)
        """konecz zagruzki"""
        self.player_design = (
            self.player_design_right
        )  # Исправлено: используем правильную переменную
        self.image = self.player_design  # Добавлено для pygame.sprite.Sprite
        """leave"""
        self.player_name = name
        self.player_lifes = 3
        self.player_leavel = 1
        self.rect.x = 0
        self.rect.y = 0
        self.jump = False
        self.ind_jump = 0
        self.direction = 0
        self.count = 0
        self.index = 0
        self.width = self.player_design.get_width()
        self.height = self.player_design.get_height()
        self.gravity = 0

    def update(self):
        game_over = 0
        x = 0
        y = 0
        self.gravity += 1
        if self.gravity > 10:
            self.gravity = 10
        if self.rect.bottom > screen_size:
            self.rect.bottom = screen_size

        # Получаем состояние клавиш
        key = pg.key.get_pressed()

        # Сброс направления если нет движения
        moving = False

        # Движение вправо
        if key[pg.K_d]:
            if self.rect.x < screen_size - 32:  # Исправлена логика границ
                x += 5
                self.direction = 0
                self.count += 1
                moving = True
            else:
                print("nelza pravo")

        # Движение влево
        if key[pg.K_a]:
            if self.rect.x > 0:  # Исправлена логика границ
                x -= 5
                self.direction = 1
                self.count += 1
                moving = True
            else:
                print("nelza levo")

        # Прыжок
        if key[pg.K_SPACE] and self.jump == False:
            self.jump = True
            self.gravity = -15

        """if self.jump:
            if self.ind_jump < 15:
                y -= 5
                self.ind_jump += 1
            else:
                self.jump = False
        elif self.player_rect.y < screen_size - 124:
           y += 5"""
        if self.count > 5:
            self.count = 0
            self.index += 1
            if self.index >= len(self.player_right):
                self.index = 0
            if self.direction == 0:
                self.player_design = self.player_right[self.index]
            elif self.direction == 1:
                self.player_design = self.player_left[self.index]
            # Обновляем image для pygame.sprite
            self.image = self.player_design

        # screen.blit(map_save, (0, 0))
        for block in map.map_list:
            if block[1].colliderect(
                self.rect.x + x, self.rect.y, self.width, self.height
            ):
                x = 0
            if block[1].colliderect(
                self.rect.x, self.rect.y + y, self.width, self.height
            ):
                if self.gravity < 0:
                    y = block[1].bottom - self.rect.top
                    self.gravity = 0
                elif self.gravity >= 0:
                    y = block[1].top - self.rect.bottom
                    self.gravity = 0
                    self.jump = False
        y += self.gravity
        if pg.sprite.spritecollide(self, lava_group, False):
            game_over = -1
        elif pg.sprite.spritecollide(self, exit_group, False):
            game_over = 1
        if game_over == -1:
            print("loser")
        elif game_over == 1:
            print("maladsar")
        self.rect.x += x
        self.rect.y += y
        screen.blit(self.player_design, self.rect)


class Map:

    def __init__(self, data):
        """zagruzka blockov"""
        grass_block = pg.image.load(f"block_designs/grass/block_grass_0.png")
        dirt_block = pg.image.load(f"block_designs/grass/block_dirt_0.png")
        self.map_list = []
        with open("level_base.json", "r") as map_loading:
            self.map = j.load(map_loading)
        row_count = 0
        for map in self.map:
            for row in map:
                col_count = 0
                for block in row:
                    try:
                        if block == 1 or block == 2:
                            images = {1: dirt_block, 2: grass_block}
                            img = pg.transform.scale(
                                images[block], (block_size, block_size)
                            )
                            img_rect = img.get_rect()
                            img_rect.x = col_count * block_size
                            img_rect.y = row_count * block_size
                            block = (img, img_rect)
                            self.map_list.append(block)
                            print("succsecuful")
                        elif block == 3:
                            lava = Lava(col_count * block_size, row_count * block_size)
                            lava_group.add(lava)
                        elif block == 4:
                            exit = Door(
                                col_count * block_size,
                                row_count * block_size - (block_size // 2),
                            )
                            exit_group.add(exit)
                            print("yeah")
                    except Exception as e:
                        print("Oshibka:", e)
                    print(block)
                    col_count += 1
                row_count += 1
                # self.map_column.clear()
            print(self.map_list)
        # self.grass_edge = []
        # index = 0
        """govno
        for i in range(3):
            time = pg.image.load(f'block_designs/grass/block_grass_{i}.png')
            pg.transform.scale(pg.image.load(f'block_designs/grass/block_grass_0.png'), (64, 64))
        for i in range(3):
            time = pg.image.load(f'block_designs/grass/block_dirt_{i}.png')
            self.dirt_blocks.append(pg.transform.scale(pg.image.load(f'block_designs/grass/block_dirt_{i}.png'), (64, 64)))

        time = pg.image.load('block_designs/grass/block_grass_out_edge.png')
        self.grass_edge.append(pg.transform.scale(time, (64, 64)))
        self.grass_edge.append(pg.transform.flip(pg.transform.scale(time, (64, 64)), True, False))

        time = pg.image.load('block_designs/grass/block_grass_in_edge.png')
        self.grass_edge.append(pg.transform.scale(time, (64, 64)))
        self.grass_edge.append(pg.transform.flip(pg.transform.scale(time, (64, 64)), True, False))


        blocki sdelani

        self.created = False
        self.blocks = {
            0: self.sky,
            1: self.ranomizer_dirt,
            2: self.ranomizer_grass,
            3: self.grass_edge[0],
            4: self.grass_edge[1],
            5: self.grass_edge[2],
            6: self.grass_edge[3],
            7: lambda x, y, column: self.create_lava(x, y, column),
            8: lambda x, y, column: self.create_lava(x, y, column),
        }"""

    def draw(self):
        for block in self.map_list:
            # map_save.blit(block[0], block[1])
            screen.blit(block[0], block[1])


class Lava(pg.sprite.Sprite):
    def __init__(self, x, y):
        super().__init__()
        img = pg.image.load("block_designs/Lava/lava_top.png")
        self.image = pg.transform.scale(img, (block_size, block_size))
        self.rect = self.image.get_rect()
        self.rect.x = x
        self.rect.y = y


class Door(pg.sprite.Sprite):
    def __init__(self, x, y):
        print("nigga")
        pg.sprite.Sprite.__init__(self)
        img = pg.image.load("exit_door.png")
        self.image = self.image = pg.transform.scale(
            img, (block_size, block_size * 1.5)
        )
        self.rect = self.image.get_rect()
        self.rect.x = x
        self.rect.y = y


class Button:
    def __init__(self, x, y, image):
        self.image = pg.image.load(image)
        self.rect = self.image.get_rect(center=(x, y))

    def draw(self):
        action = False
        if self.rect.collidepoint(pg.mouse.get_pos()):
            if pg.mouse.get_pressed()[0] == 1:
                action = True
        screen.blit(self.image, self.rect)
        return action


start_button = Button(
    screen_size // 2 - 150, screen_size // 2 - 150, "desings/start_btn.png"
)
exit_button = Button(
    screen_size // 2 - 150, screen_size // 2 + 150, "desings/exit_btn.png"
)
restart_button = Button(
    screen_size // 2 - 150, screen_size // 2 - 75, "desings/restart_btn.png"
)
lava_group = pg.sprite.Group()
exit_group = pg.sprite.Group()
map_save = pg.Surface((screen_size, screen_size))
player1 = Player("krutoi")
map = Map("nothing")
main_menu = True
while game_running:
    # Обработка событий в начале цикла для лучшей отзывчивости
    for event in pg.event.get():
        if event.type == pg.QUIT:
            game_running = False

    if main_menu:
        if start_button.draw():
            main_menu = False
        if exit_button.draw():
            main_menu = False
            game_running = False
    else:
        screen.fill((169, 193, 255))
        map.draw()
        # screen.blit(map_save, (0, 0))
        lava_group.draw(screen)
        exit_group.draw(screen)
        player1.update()
        lava_group.update()

    pg.display.update()
    fps.tick(60)
pg.quit()
quit()
